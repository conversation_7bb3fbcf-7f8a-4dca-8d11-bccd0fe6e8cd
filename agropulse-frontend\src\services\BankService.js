import axios from 'axios'

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api'

class BankService {
  constructor() {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    // Add auth token to requests
    this.api.interceptors.request.use((config) => {
      const token = localStorage.getItem('token')
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }
      return config
    })
  }

  // Bank Accounts
  async getBankAccounts() {
    try {
      const response = await this.api.get('/bank/accounts')
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async addBankAccount(accountData) {
    try {
      const response = await this.api.post('/bank/accounts', accountData)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async updateBankAccount(accountId, accountData) {
    try {
      const response = await this.api.put(`/bank/accounts/${accountId}`, accountData)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async deleteBankAccount(accountId) {
    try {
      const response = await this.api.delete(`/bank/accounts/${accountId}`)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async verifyBankAccount(accountId, verificationData) {
    try {
      const response = await this.api.post(`/bank/accounts/${accountId}/verify`, verificationData)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  // Payment Cards
  async getPaymentCards() {
    try {
      const response = await this.api.get('/bank/cards')
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async addPaymentCard(cardData) {
    try {
      const response = await this.api.post('/bank/cards', cardData)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async updatePaymentCard(cardId, cardData) {
    try {
      const response = await this.api.put(`/bank/cards/${cardId}`, cardData)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async deletePaymentCard(cardId) {
    try {
      const response = await this.api.delete(`/bank/cards/${cardId}`)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async setDefaultPaymentMethod(methodId, methodType) {
    try {
      const response = await this.api.post('/bank/set-default', {
        method_id: methodId,
        method_type: methodType // 'account' or 'card'
      })
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  // Transactions
  async getTransactions(filters = {}) {
    try {
      const response = await this.api.get('/bank/transactions', { params: filters })
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async getTransactionDetails(transactionId) {
    try {
      const response = await this.api.get(`/bank/transactions/${transactionId}`)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  // Balance and Account Info
  async getAccountBalance(accountId) {
    try {
      const response = await this.api.get(`/bank/accounts/${accountId}/balance`)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async getAllBalances() {
    try {
      const response = await this.api.get('/bank/balances')
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  // Wire Transfers
  async initiateWireTransfer(transferData) {
    try {
      const response = await this.api.post('/bank/wire-transfer', transferData)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async getWireTransfers() {
    try {
      const response = await this.api.get('/bank/wire-transfers')
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async getWireTransferStatus(transferId) {
    try {
      const response = await this.api.get(`/bank/wire-transfers/${transferId}/status`)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  // Security and Verification
  async enableTwoFactorAuth() {
    try {
      const response = await this.api.post('/bank/security/enable-2fa')
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async disableTwoFactorAuth(verificationCode) {
    try {
      const response = await this.api.post('/bank/security/disable-2fa', {
        verification_code: verificationCode
      })
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async verifyTransaction(transactionId, verificationCode) {
    try {
      const response = await this.api.post(`/bank/transactions/${transactionId}/verify`, {
        verification_code: verificationCode
      })
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  // Supported Banks and Validation
  async getSupportedBanks() {
    try {
      const response = await this.api.get('/bank/supported-banks')
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async validateAccountNumber(bankCode, accountNumber) {
    try {
      const response = await this.api.post('/bank/validate-account', {
        bank_code: bankCode,
        account_number: accountNumber
      })
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async validateRoutingNumber(routingNumber) {
    try {
      const response = await this.api.post('/bank/validate-routing', {
        routing_number: routingNumber
      })
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  // Exchange Rates
  async getExchangeRates() {
    try {
      const response = await this.api.get('/bank/exchange-rates')
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async convertCurrency(amount, fromCurrency, toCurrency) {
    try {
      const response = await this.api.post('/bank/convert-currency', {
        amount,
        from_currency: fromCurrency,
        to_currency: toCurrency
      })
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  // Reports and Statements
  async getAccountStatement(accountId, startDate, endDate) {
    try {
      const response = await this.api.get(`/bank/accounts/${accountId}/statement`, {
        params: {
          start_date: startDate,
          end_date: endDate
        },
        responseType: 'blob'
      })
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async getTaxReport(year) {
    try {
      const response = await this.api.get(`/bank/tax-report/${year}`, {
        responseType: 'blob'
      })
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  // Handle API errors
  handleError(error) {
    if (error.response) {
      // Server responded with error status
      const message = error.response.data?.message || 'An error occurred'
      return new Error(message)
    } else if (error.request) {
      // Request was made but no response received
      return new Error('Network error - please check your connection')
    } else {
      // Something else happened
      return new Error('An unexpected error occurred')
    }
  }
}

export default new BankService()
