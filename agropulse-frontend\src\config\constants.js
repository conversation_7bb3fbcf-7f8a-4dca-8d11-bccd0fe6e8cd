/**
 * Application-wide constants
 */

// API URLs
export const API_BASE_URL = import.meta.env.PROD
  ? 'https://api.agropulse.com/api'
  : 'http://localhost:8000/api';

export const AI_API_URL = import.meta.env.PROD
  ? 'https://ai-api.agropulse.com/api'
  : 'http://127.0.0.1:5000';

// External APIs
export const EXTERNAL_APIS = {
  FAO: 'https://api.fao.org/v1',
  WORLD_BANK: 'https://api.worldbank.org/v2',
  OPEN_WEATHER: 'https://api.openweathermap.org/data/2.5',
  GOOGLE_MAPS: 'https://maps.googleapis.com/maps/api'
};

// Authentication
export const AUTH = {
  TOKEN_KEY: 'agropulse_token',
  REFRESH_TOKEN_KEY: 'agropulse_refresh_token',
  USER_KEY: 'agropulse_user',
  TOKEN_EXPIRY: 60 * 60 * 1000, // 1 hour in milliseconds
};

// User Roles
export const USER_ROLES = {
  ADMIN: 'admin',
  MODERATOR: 'moderator',
  FARMER: 'farmer',
  IMPORTER: 'importer',
  SHIPPING: 'shipping'
};

// User Status
export const USER_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  PENDING: 'pending',
  SUSPENDED: 'suspended'
};

// Product Categories
export const PRODUCT_CATEGORIES = [
  'Fruits',
  'Vegetables',
  'Grains',
  'Nuts',
  'Seeds',
  'Dairy',
  'Meat',
  'Poultry',
  'Seafood',
  'Herbs',
  'Spices',
  'Oils',
  'Organic',
  'Processed Foods'
];

// Contract Status
export const CONTRACT_STATUS = {
  DRAFT: 'draft',
  PENDING: 'pending',
  ACTIVE: 'active',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled',
  DISPUTED: 'disputed'
};

// Shipment Status
export const SHIPMENT_STATUS = {
  PREPARING: 'preparing',
  IN_TRANSIT: 'in_transit',
  DELIVERED: 'delivered',
  DELAYED: 'delayed',
  CANCELLED: 'cancelled'
};

// Payment Methods
export const PAYMENT_METHODS = [
  'Credit Card',
  'Bank Transfer',
  'PayPal',
  'Apple Pay',
  'Google Pay',
  'Cash on Delivery'
];

// Payment Status
export const PAYMENT_STATUS = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  FAILED: 'failed',
  REFUNDED: 'refunded'
};

// Advertisement Types
export const ADVERTISEMENT_TYPES = {
  BANNER: 'banner',
  SIDEBAR: 'sidebar',
  POPUP: 'popup',
  FEATURED: 'featured',
  PPC: 'ppc'
};

// Sustainability Certification Types
export const CERTIFICATION_TYPES = [
  'Organic',
  'Fair Trade',
  'Rainforest Alliance',
  'USDA Organic',
  'Non-GMO Project',
  'Carbon Neutral',
  'Water Footprint',
  'Biodiversity Friendly'
];

// Sustainable Practices
export const SUSTAINABLE_PRACTICES = [
  'Crop Rotation',
  'Water Conservation',
  'Renewable Energy Use',
  'Reduced Packaging',
  'Composting',
  'Integrated Pest Management',
  'Soil Conservation',
  'Biodiversity Protection'
];

// Pagination
export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 10,
  MAX_LIMIT: 100
};

// File Upload
export const FILE_UPLOAD = {
  MAX_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
  IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/gif'],
  DOCUMENT_TYPES: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
};

// Date Formats
export const DATE_FORMATS = {
  DISPLAY: 'MMM DD, YYYY',
  API: 'YYYY-MM-DD',
  DATETIME_DISPLAY: 'MMM DD, YYYY HH:mm',
  DATETIME_API: 'YYYY-MM-DD HH:mm:ss'
};

// Currency
export const DEFAULT_CURRENCY = 'USD';
export const SUPPORTED_CURRENCIES = ['USD', 'EUR', 'GBP', 'JPY', 'AUD', 'CAD', 'CHF', 'CNY', 'INR', 'EGP'];

// Languages
export const SUPPORTED_LANGUAGES = [
  { code: 'en', name: 'English', direction: 'ltr' },
  { code: 'ar', name: 'العربية', direction: 'rtl' },
  { code: 'fr', name: 'Français', direction: 'ltr' },
  { code: 'es', name: 'Español', direction: 'ltr' }
];

// Default settings
export const DEFAULT_SETTINGS = {
  language: 'en',
  currency: 'USD',
  theme: 'light',
  notifications: true,
  emailNotifications: true
};

// Export all constants as a default object
export default {
  API_BASE_URL,
  AI_API_URL,
  EXTERNAL_APIS,
  AUTH,
  USER_ROLES,
  USER_STATUS,
  PRODUCT_CATEGORIES,
  CONTRACT_STATUS,
  SHIPMENT_STATUS,
  PAYMENT_METHODS,
  PAYMENT_STATUS,
  ADVERTISEMENT_TYPES,
  CERTIFICATION_TYPES,
  SUSTAINABLE_PRACTICES,
  PAGINATION,
  FILE_UPLOAD,
  DATE_FORMATS,
  DEFAULT_CURRENCY,
  SUPPORTED_CURRENCIES,
  SUPPORTED_LANGUAGES,
  DEFAULT_SETTINGS
};
