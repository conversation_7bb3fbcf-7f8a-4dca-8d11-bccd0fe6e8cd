// API Configuration
export const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api'
export const AI_API_URL = import.meta.env.VITE_AI_API_URL || 'http://127.0.0.1:5000'

// Use mock data for development/testing
export const USE_MOCK_DATA = import.meta.env.VITE_USE_MOCK_DATA === 'true' || true

// API endpoints
export const API_ENDPOINTS = {
  // Auth
  LOGIN: '/auth/login',
  REGISTER: '/auth/register',
  LOGOUT: '/auth/logout',
  REFRESH: '/auth/refresh',

  // Users
  USERS: '/users',
  USER_PROFILE: '/user/profile',

  // Products
  PRODUCTS: '/products',

  // Contracts
  CONTRACTS: '/contracts',

  // Shipments
  SHIPMENTS: '/shipments',

  // Advertisements
  ADVERTISEMENTS: '/advertisements',

  // Payments
  PAYMENTS: '/payments',

  // Admin
  ADMIN: '/admin',

  // AI
  AI_PRICE_PREDICTION: '/ai/price-prediction',
  AI_DEMAND_SUPPLY: '/ai/demand-supply',
  AI_CLIMATE_IMPACT: '/ai/climate-impact',
  AI_RECOMMENDATIONS: '/ai/recommendations',
  AI_SEASONAL_ANALYSIS: '/ai/seasonal-analysis',
  AI_SMART_PRICING: '/ai/smart-pricing',
  AI_MISSING_PRODUCTS: '/ai/missing-products',
  AI_COUNTRY_ANALYSIS: '/ai/country-analysis'
}

// Request timeout
export const REQUEST_TIMEOUT = 30000

// Default headers
export const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}
