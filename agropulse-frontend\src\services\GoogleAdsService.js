import axios from 'axios'

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api'

class GoogleAdsService {
  constructor() {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    // Add auth token to requests
    this.api.interceptors.request.use((config) => {
      const token = localStorage.getItem('token')
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }
      return config
    })
  }

  // Account Management
  async connectAccount() {
    try {
      const response = await this.api.post('/google-ads/connect')
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async disconnectAccount() {
    try {
      const response = await this.api.post('/google-ads/disconnect')
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async getAccountInfo() {
    try {
      const response = await this.api.get('/google-ads/account')
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  // Campaigns
  async getCampaigns() {
    try {
      const response = await this.api.get('/google-ads/campaigns')
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async createCampaign(campaignData) {
    try {
      const response = await this.api.post('/google-ads/campaigns', campaignData)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async updateCampaign(campaignId, campaignData) {
    try {
      const response = await this.api.put(`/google-ads/campaigns/${campaignId}`, campaignData)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async pauseCampaign(campaignId) {
    try {
      const response = await this.api.post(`/google-ads/campaigns/${campaignId}/pause`)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async resumeCampaign(campaignId) {
    try {
      const response = await this.api.post(`/google-ads/campaigns/${campaignId}/resume`)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async deleteCampaign(campaignId) {
    try {
      const response = await this.api.delete(`/google-ads/campaigns/${campaignId}`)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  // Ad Groups
  async getAdGroups(campaignId) {
    try {
      const response = await this.api.get(`/google-ads/campaigns/${campaignId}/ad-groups`)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async createAdGroup(campaignId, adGroupData) {
    try {
      const response = await this.api.post(`/google-ads/campaigns/${campaignId}/ad-groups`, adGroupData)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async updateAdGroup(adGroupId, adGroupData) {
    try {
      const response = await this.api.put(`/google-ads/ad-groups/${adGroupId}`, adGroupData)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  // Ads
  async getGoogleAds(campaignId = null) {
    try {
      const url = campaignId ? `/google-ads/campaigns/${campaignId}/ads` : '/google-ads/ads'
      const response = await this.api.get(url)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async createAd(adGroupId, adData) {
    try {
      const response = await this.api.post(`/google-ads/ad-groups/${adGroupId}/ads`, adData)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async updateAd(adId, adData) {
    try {
      const response = await this.api.put(`/google-ads/ads/${adId}`, adData)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async pauseAd(adId) {
    try {
      const response = await this.api.post(`/google-ads/ads/${adId}/pause`)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async resumeAd(adId) {
    try {
      const response = await this.api.post(`/google-ads/ads/${adId}/resume`)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  // Keywords
  async getKeywords(adGroupId) {
    try {
      const response = await this.api.get(`/google-ads/ad-groups/${adGroupId}/keywords`)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async addKeywords(adGroupId, keywords) {
    try {
      const response = await this.api.post(`/google-ads/ad-groups/${adGroupId}/keywords`, {
        keywords
      })
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async updateKeyword(keywordId, keywordData) {
    try {
      const response = await this.api.put(`/google-ads/keywords/${keywordId}`, keywordData)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async removeKeyword(keywordId) {
    try {
      const response = await this.api.delete(`/google-ads/keywords/${keywordId}`)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  // Keyword Research
  async getKeywordIdeas(seedKeywords, targetLocation = null) {
    try {
      const response = await this.api.post('/google-ads/keyword-ideas', {
        seed_keywords: seedKeywords,
        target_location: targetLocation
      })
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async getKeywordMetrics(keywords) {
    try {
      const response = await this.api.post('/google-ads/keyword-metrics', {
        keywords
      })
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  // Performance Reports
  async getCampaignPerformance(campaignId, dateRange) {
    try {
      const response = await this.api.get(`/google-ads/campaigns/${campaignId}/performance`, {
        params: dateRange
      })
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async getAdGroupPerformance(adGroupId, dateRange) {
    try {
      const response = await this.api.get(`/google-ads/ad-groups/${adGroupId}/performance`, {
        params: dateRange
      })
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async getAdPerformance(adId, dateRange) {
    try {
      const response = await this.api.get(`/google-ads/ads/${adId}/performance`, {
        params: dateRange
      })
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async getKeywordPerformance(keywordId, dateRange) {
    try {
      const response = await this.api.get(`/google-ads/keywords/${keywordId}/performance`, {
        params: dateRange
      })
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  // Account Performance
  async getAccountPerformance(dateRange) {
    try {
      const response = await this.api.get('/google-ads/account/performance', {
        params: dateRange
      })
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  // Budget Management
  async getBudgetRecommendations(campaignId) {
    try {
      const response = await this.api.get(`/google-ads/campaigns/${campaignId}/budget-recommendations`)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async updateCampaignBudget(campaignId, budget) {
    try {
      const response = await this.api.put(`/google-ads/campaigns/${campaignId}/budget`, {
        budget
      })
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  // Bid Management
  async getBidRecommendations(adGroupId) {
    try {
      const response = await this.api.get(`/google-ads/ad-groups/${adGroupId}/bid-recommendations`)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async updateKeywordBid(keywordId, bid) {
    try {
      const response = await this.api.put(`/google-ads/keywords/${keywordId}/bid`, {
        bid
      })
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  // Targeting
  async getLocationTargets(campaignId) {
    try {
      const response = await this.api.get(`/google-ads/campaigns/${campaignId}/location-targets`)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async updateLocationTargets(campaignId, locations) {
    try {
      const response = await this.api.put(`/google-ads/campaigns/${campaignId}/location-targets`, {
        locations
      })
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async getAudienceTargets(campaignId) {
    try {
      const response = await this.api.get(`/google-ads/campaigns/${campaignId}/audience-targets`)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async updateAudienceTargets(campaignId, audiences) {
    try {
      const response = await this.api.put(`/google-ads/campaigns/${campaignId}/audience-targets`, {
        audiences
      })
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  // Extensions
  async getSitelinks(campaignId) {
    try {
      const response = await this.api.get(`/google-ads/campaigns/${campaignId}/sitelinks`)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async addSitelinks(campaignId, sitelinks) {
    try {
      const response = await this.api.post(`/google-ads/campaigns/${campaignId}/sitelinks`, {
        sitelinks
      })
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  // Conversion Tracking
  async getConversions(campaignId, dateRange) {
    try {
      const response = await this.api.get(`/google-ads/campaigns/${campaignId}/conversions`, {
        params: dateRange
      })
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async createConversionAction(conversionData) {
    try {
      const response = await this.api.post('/google-ads/conversion-actions', conversionData)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  // Handle API errors
  handleError(error) {
    if (error.response) {
      // Server responded with error status
      const message = error.response.data?.message || 'An error occurred'
      return new Error(message)
    } else if (error.request) {
      // Request was made but no response received
      return new Error('Network error - please check your connection')
    } else {
      // Something else happened
      return new Error('An unexpected error occurred')
    }
  }
}

export default new GoogleAdsService()
